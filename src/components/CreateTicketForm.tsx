
import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import TerminalWindow from './TerminalWindow';

interface CreateTicketFormProps {
  onTicketCreated: () => void;
}

const CreateTicketForm: React.FC<CreateTicketFormProps> = ({ onTicketCreated }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    try {
      const { data: ticket, error: ticketError } = await supabase
        .from('tickets')
        .insert({
          user_id: user.id,
          title,
          description,
          priority,
          status: 'pending_payment',
          payment_status: 'pending'
        })
        .select()
        .single();

      if (ticketError) throw ticketError;

      // Initiate payment
      const { data: paymentData, error: paymentError } = await supabase.functions.invoke(
        'create-ticket-payment',
        {
          body: { ticketId: ticket.id }
        }
      );

      if (paymentError) throw paymentError;

      // Redirect to Stripe Checkout
      if (paymentData?.url) {
        window.open(paymentData.url, '_blank');
      }

      toast({
        title: "Ticket Created!",
        description: "Please complete the payment to submit your ticket.",
      });

      setTitle('');
      setDescription('');
      setPriority('medium');
      onTicketCreated();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create ticket",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <TerminalWindow title="CREATE_TICKET.EXE">
      <div className="space-y-6">
        <div className="text-center mb-6">
          <div className="text-lg font-bold text-retro-green mb-2">
            &gt; NEW SUPPORT REQUEST
          </div>
          <div className="text-sm opacity-80">
            Submit a support ticket for $29.99 - Get priority help from our team
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label className="text-retro-green">TICKET TITLE:</Label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="bg-black border-retro-green text-retro-green focus:border-retro-green"
              placeholder="Brief description of your issue..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label className="text-retro-green">PRIORITY LEVEL:</Label>
            <Select value={priority} onValueChange={(value: any) => setPriority(value)}>
              <SelectTrigger className="bg-black border-retro-green text-retro-green">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-black border-retro-green">
                <SelectItem value="low" className="text-retro-green">LOW - General inquiry</SelectItem>
                <SelectItem value="medium" className="text-retro-green">MEDIUM - Standard issue</SelectItem>
                <SelectItem value="high" className="text-retro-green">HIGH - Business impacting</SelectItem>
                <SelectItem value="urgent" className="text-retro-green">URGENT - Critical system down</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label className="text-retro-green">ISSUE DESCRIPTION:</Label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="bg-black border-retro-green text-retro-green focus:border-retro-green min-h-[120px]"
              placeholder="Detailed description of your issue, steps to reproduce, error messages, etc..."
              required
            />
          </div>

          <div className="flex gap-4 pt-4">
            <Button
              type="submit"
              disabled={loading}
              className="flex-1 bg-retro-green text-black hover:bg-retro-green/90 font-mono"
            >
              {loading ? 'PROCESSING...' : 'CREATE TICKET - $29.99'}
            </Button>
          </div>
        </form>
      </div>
    </TerminalWindow>
  );
};

export default CreateTicketForm;
