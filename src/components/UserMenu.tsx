
import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { LogOut, User } from 'lucide-react';
import RetroButton from './RetroButton';

const UserMenu = () => {
  const { user, signOut } = useAuth();

  if (!user) return null;

  return (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-2 text-sm">
        <User className="w-4 h-4" />
        <span>{user.email}</span>
      </div>
      <RetroButton
        onClick={signOut}
        className="flex items-center gap-2 text-xs px-3 py-1"
      >
        <LogOut className="w-3 h-3" />
        LOGOUT
      </RetroButton>
    </div>
  );
};

export default UserMenu;
